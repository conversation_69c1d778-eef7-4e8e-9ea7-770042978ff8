/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
     font-family: "Outfit", sans-serif !important;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
}
body p{
	color:#000;
	font-size:18px;
}

body h2{
	    font-size: 2.5rem;
}
.about-content-data h2 {
    margin: 0 0 15px 0;
}
section.about-sec {
    padding: 0 0 80px 0;
}
/* Navigation Styles */
nav.navbar {
    background-color: #ffffff;
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
.navbar img {
    max-width: 250px;
}
.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.nav-menu {
    display: flex;
    list-style: none;
}

.nav-menu li.nav-item .nav-link {
	font-size:20px;
    color: #000;
    text-decoration: none;
    transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: 	#3b306c;
}
ul.nav-menu {
    margin: 0;
    padding: 0;
	align-items: center;
}
.nav-menu li.nav-item a {
    position: relative;
	margin-right:50px;
}
.nav-menu li.nav-item a::before {
    height: 2px;
    width: 0;
    content: "";
    position: absolute;
    background: #3b306c;
    left: 50%;
    bottom: -27px;
    transform: translateX(-50%);
    transition: all 0.5s ease;
}
section.inner-banner {
    background-image: url(../images/home-bg.png);
    background-position: center;
    background-size: cover;
    color: white;
    text-align: center;
	    padding: 80px 0;
}
.nav-menu li.nav-item a:hover::before {
    width: 100%;
    transition: all 0.5s ease;
}
.form-group.checkbox a {
    color: #1aadc4;
    font-weight: 500;
}
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: #ecf0f1;
    margin: 3px 0;
    transition: 0.3s;
}
.nav-logo a {
    font-size: 35px;
    text-decoration: none;
    color: #000;
    font-weight: bold;
}
/* Main Content */
.main-content {
    margin-top: 80px;
    min-height: calc(100vh - 160px);
}

/* Hero Section */
.hero {
    background-image: url(../images/home-bg.png);
    background-position: center;
    background-size: cover;
    color: white;
    padding: 150px 0;
    text-align: center;
}

.hero-content h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
	    color: #12aec6;
		    font-weight: bold;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
	color:#000;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}
.hero-buttons a.btn.btn-primary {
    box-shadow: 0 10px 30px 3px rgb(0 187 120 / 33%);
}
.hero-buttons a.btn.btn-secondary {
    box-shadow: 0 10px 30px 3px rgb(103 58 183 / 29%);
}
/* Button Styles */
.hero-buttons .btn {
    display: flex;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 18px;
	align-items: center;
}
.typing-text {
  display: inline-block;
  width: 9ch;
  white-space: nowrap;
  color:#ff6068;
  overflow: hidden;
  vertical-align: bottom;
  animation: smoothTyping 4s linear infinite;
}

@keyframes smoothTyping {
  0% { width: 0ch; opacity: 0; }
  10% { opacity: 1; }
  50% { width: 9ch; }
  80% { opacity: 1; }
  100% { width: 0ch; opacity: 0; }
}
.hero-buttons .btn-secondary {
    background: #ff6068;
    border-color: #ff6068;
}
li.nav-item.nav-button.login a.nav-link {
    background: #12aec6;
}
.hero-buttons .btn-primary {
    background-color:#12aec6;
    color: white;
}
.hero-buttons .btn-primary:hover{background: #3b306c;}
.btn-primary:hover {
    background-color: #2980b9;
}
li.nav-item.nav-button a.nav-link {
    background: #ff6068;
    color: #fff;
    padding: 10px 30px;
    margin: 0 6px;
    border-radius: 10px;
    font-size: 17px;
    display: inline-block;
	text-transform: uppercase;
	font-weight:600;
}
li.nav-item.nav-button a.nav-link::before {
    display: none;
}
.btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background-color: white;
    color: #333;
}

.btn-full {
    width: 100%;
}

/* Features Section */
.features {
    padding: 80px 0;
    background-color: white;
}
.contact-content {
    margin: 50px 0 0 0;
}
.contact-info h2 {
    margin: 0 0 30px 0;
}
.features h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
}
.contact-item i {
    color: #1aadc4;
    font-size: 26px;
}
.contact-item {
    display: flex;
    align-items: baseline;
    gap: 15px;
}
.auth-form.register-form {
    max-width: 750px;
}
.form-group.checkbox {
    display: flex;
    align-items: center;
}
form.global-form label {
    font-weight: 400;
    color: #000;
}
.form-group.checkbox label {
    margin: 0;
}
.auth-links p {
    line-height: 25px;
    margin: 0;
}
.global-form button.global-btn {
    margin: 0;
    width: 100%;
}
.auth-links a {
    color: #12aec6;
    font-weight: 500;
    text-decoration: underline;
}
.contact-item h3 {
    color: #000;
    font-size: 22px;
	margin:0 0 10px 0;
}
button.global-btn {
    background: #ff6068;
    color: #fff;
    padding: 10px 30px;
    margin: 0 6px;
    border-radius: 10px;
    font-size: 17px;
    display: inline-block;
    text-transform: uppercase;
    font-weight: 600;
    border: none;
    transition: all ease 0.3s;
}
button.global-btn:hover{
	background:#12aec6;
}
section.about-sec.about-page-sec {
    padding: 80px 0;
}
section.contact-section h1 {
    margin: 0;
    color: #000;
}
.inner-contact {
    display: inline-block;
    margin: 30px 0;
    width: 100%;
}
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
      box-shadow: 0 20px 50px 5px #e9eef7;
    transition: transform 0.3s ease;
}
.feat-icon {
    width: 100px;
    height: 100px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50px;
	box-shadow: 0 10px 30px 3px rgb(63 81 181 / 14%);
}
.about-content-data {
    padding: 0 0 0 40px;
}
.about-content-data ul {
    padding: 0;
    margin: 0;
}
.about-content-data ul li {    list-style: none; margin: 0 0 10px 0; color: #000; font-size: 18px; line-height: 28px; position: relative; padding: 0 0 0 20px;}
.about-content-data ul li::after {
    position: absolute;
    content: '';
    top: 10px;
    left: 0;
    width: 8px;
    height: 8px;
    background: #12aec6;
    border-radius: 5px;
}
.about-content-data ul li img {
    max-width: 24px;
    margin: 0 10px 0 0;
}
.about-content-data ul li:last-child {
    margin: 0;
}
span.subtitle {
    color: #ff6068;
    font-size: 20px;
    letter-spacing: 3px;
}
.feature-card:hover {
    transform: translateY(-5px);
}
.feature-card a {
    position: relative;
    border-radius: 50px;
    text-decoration: none;
    background: #ff6068;
    padding: 10px 30px;
    color: #ffffff;
    text-transform: uppercase;
    margin: 20px 0 0 0;
    display: inline-block;
    font-weight: 600;
    font-size: 18px;
    overflow: hidden;
    z-index: 1;
    transition: all 0.4s ease;
}

/* Animated background overlay */
.feature-card a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: #12aec6; /* Your custom hover color */
    transition: all 0.4s ease;
    z-index: -1;
}

/* Trigger the background slide on hover */
.feature-card a:hover::before {
    left: 0;
}

/* Optional: Add scale and shadow on hover */
.feature-card a:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(59, 48, 108, 0.4); /* matches the hover color */
}

.feature-card h3 {
     margin: 15px 0;
	    color: #12aec6;
    font-weight: bold;
}

/* Authentication Forms */
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 160px);
    padding: 2rem 0;
}

.auth-form {
    background-color: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
}

.auth-form h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #000;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #555;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: 0.5rem;
}
.auth-form.login-form {
    max-width: 650px;
}
.auth-links {
    text-align: center;
    margin-top: 1rem;
}

div.auth-links a:hover {
    text-decoration: underline;
	color:#ff6068;
}

/* About and Contact Pages */
.about-section,
.contact-section {
    padding: 2rem 0;
    background-color: white;
    margin: 2rem 0;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.about-section h1,
.contact-section h1 {
    text-align: center;
    margin-bottom: 2rem;
    color: #2c3e50;
}

.about-content,
.contact-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 0 2rem;
}

.contact-content {
    grid-template-columns: 1fr 1fr;
}

.contact-info {
    background-color: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
}

.contact-item {
    margin-bottom: 1.5rem;
}
div.assistant-button {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #1aadc4 0%, #ff5d6a 100%);
    border-radius: 50%;
    display: flex
;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    color: white;
}

/* Footer */
.footer {
    background-color: #12aec6;
    color: #ecf0f1;
    text-align: center;
    padding: 15px 0;
    margin-top: auto;
}
.footer p {
    margin: 0;
    color: #fff;
}
/* Responsive Design */
@media screen and (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: #2c3e50;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
    }
}
