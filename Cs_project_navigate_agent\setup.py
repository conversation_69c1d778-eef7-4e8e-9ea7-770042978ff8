#!/usr/bin/env python3
"""
Setup script for Voice Navigation Assistant
"""

import subprocess
import sys
import os

def install_requirements():
    """Install Python requirements"""
    print("Installing Python requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Python requirements installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False
    return True

def check_env_file():
    """Check if .env file exists and has OpenAI API key"""
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        return False
    
    with open('.env', 'r') as f:
        content = f.read()
        if 'your_openai_api_key_here' in content:
            print("⚠️  Please update your OpenAI API key in the .env file")
            return False
    
    print("✅ .env file configured!")
    return True

def main():
    print("🚀 Setting up Voice Navigation Assistant...")
    print("=" * 50)
    
    # Install requirements
    if not install_requirements():
        sys.exit(1)
    
    # Check environment file
    env_ok = check_env_file()
    
    print("\n" + "=" * 50)
    print("Setup Summary:")
    print("✅ Python dependencies installed")
    
    if env_ok:
        print("✅ Environment configured")
        print("\n🎉 Setup complete! You can now run:")
        print("   python main.py")
        print("\nThen open your website and try voice commands like:")
        print("   - 'Open home page'")
        print("   - 'Go to about'")
        print("   - 'Navigate to contact'")
    else:
        print("⚠️  Please update your OpenAI API key in .env file")
        print("   1. Get your API key from: https://platform.openai.com/api-keys")
        print("   2. Replace 'your_openai_api_key_here' in .env file")
        print("   3. Run: python main.py")

if __name__ == "__main__":
    main()
