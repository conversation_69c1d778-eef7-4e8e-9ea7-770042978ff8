# Voice Navigation Assistant

A voice-controlled navigation assistant for your website that uses speech-to-text and OpenAI API to understand and execute navigation commands.

## Features

- 🎤 **Voice Recognition**: Uses Google's Web Speech API for speech-to-text
- 🤖 **AI-Powered**: OpenAI GPT-3.5 for natural language understanding
- 🚀 **FastAPI Backend**: High-performance API for processing commands
- 📱 **Responsive UI**: Beautiful floating assistant button in bottom-right corner
- 🎯 **Smart Navigation**: Understands various ways to request page navigation

## Setup Instructions

### 1. Install Dependencies

Run the setup script:
```bash
python setup.py
```

Or manually install:
```bash
pip install -r requirements.txt
```

### 2. Configure OpenAI API Key

1. Get your OpenAI API key from [OpenAI Platform](https://platform.openai.com/api-keys)
2. Open the `.env` file
3. Replace `your_openai_api_key_here` with your actual API key:
   ```
   OPENAI_API_KEY=sk-your-actual-api-key-here
   ```

### 3. Start the Backend Server

```bash
python main.py
```

The FastAPI server will start on `http://localhost:8000`

### 4. Open Your Website

Open any of the HTML files in your browser:
- `index.html` (Homepage)
- `login.html` (Login page)
- `register.html` (Register page)
- `about.html` (About page)
- `contact.html` (Contact page)

## How to Use

1. **Click the Voice Assistant Button**: Look for the floating microphone button in the bottom-right corner
2. **Open the Assistant Panel**: Click the button to open the voice assistant interface
3. **Start Voice Command**: Click "Start Listening" and speak your command
4. **Navigation Commands**: Try saying:
   - "Open home page"
   - "Go to about"
   - "Navigate to contact"
   - "Take me to login"
   - "Show me the register page"

## Voice Commands Examples

The assistant understands various natural language patterns:

### Navigation Commands
- "Open home page" → Navigates to index.html
- "Go to about" → Navigates to about.html
- "Take me to contact" → Navigates to contact.html
- "Show me login" → Navigates to login.html
- "Navigate to register" → Navigates to register.html

### Supported Page Keywords
- **Home**: home, homepage, main
- **About**: about, about us
- **Contact**: contact, contact us
- **Login**: login, sign in
- **Register**: register, signup, sign up

## Technical Architecture

### Frontend (JavaScript)
- **Voice Recognition**: Web Speech API (Chrome/Edge recommended)
- **UI Components**: Custom floating assistant with responsive design
- **API Communication**: Fetch API for backend communication

### Backend (FastAPI)
- **Speech Processing**: Receives transcribed text from frontend
- **AI Processing**: OpenAI GPT-3.5 for intent recognition
- **Navigation Logic**: Maps intents to specific pages
- **CORS Enabled**: Allows cross-origin requests from frontend

### AI Integration
- **Model**: GPT-3.5-turbo for natural language understanding
- **Fallback**: Keyword matching if OpenAI API fails
- **Intent Recognition**: Extracts navigation intent and target page

## File Structure

```
├── index.html              # Homepage
├── login.html              # Login page
├── register.html           # Register page
├── about.html              # About page
├── contact.html            # Contact page
├── styles.css              # Website styling
├── script.js               # Website functionality
├── voice-assistant.js      # Voice assistant frontend
├── main.py                 # FastAPI backend server
├── requirements.txt        # Python dependencies
├── .env                    # Environment variables
├── setup.py               # Setup script
└── README.md              # This file
```

## Browser Compatibility

### Voice Recognition Support
- ✅ **Chrome** (Recommended)
- ✅ **Microsoft Edge**
- ✅ **Safari** (macOS/iOS)
- ❌ **Firefox** (Limited support)

### General Website Support
- ✅ All modern browsers
- ✅ Mobile responsive
- ✅ Desktop and tablet friendly

## Troubleshooting

### Common Issues

1. **"Speech recognition not supported"**
   - Use Chrome or Edge browser
   - Ensure HTTPS (for production) or localhost

2. **"Error processing voice command"**
   - Check if FastAPI server is running
   - Verify OpenAI API key in .env file
   - Check browser console for errors

3. **Microphone not working**
   - Allow microphone permissions in browser
   - Check system microphone settings
   - Try refreshing the page

4. **API Connection Failed**
   - Ensure FastAPI server is running on port 8000
   - Check for CORS issues in browser console
   - Verify network connectivity

### Development Tips

- **Testing**: Use browser developer tools to debug
- **Logs**: Check FastAPI server logs for backend issues
- **API Testing**: Visit `http://localhost:8000/docs` for API documentation

## Customization

### Adding New Pages
1. Add page mapping in `main.py`:
   ```python
   NAVIGATION_PAGES = {
       "services": "services.html",  # Add new page
       # ... existing pages
   }
   ```

2. Update the AI prompt to include new page in the available pages list

### Modifying Voice Commands
- Edit the OpenAI prompt in `main.py` to recognize new command patterns
- Add fallback keywords in `fallback_navigation_intent()` function

### Styling the Assistant
- Modify CSS in `voice-assistant.js` within the `createAssistantUI()` method
- Customize colors, sizes, and animations

## Security Notes

- **API Key**: Keep your OpenAI API key secure and never commit it to version control
- **CORS**: In production, restrict CORS origins to your domain
- **HTTPS**: Use HTTPS in production for speech recognition to work properly

## License

This project is open source and available under the MIT License.
