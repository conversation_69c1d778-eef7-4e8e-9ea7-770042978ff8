class VoiceNavigationAssistant {
    constructor() {
        this.isListening = false;
        this.recognition = null;
        this.apiUrl = 'http://localhost:8000';
        this.init();
    }

    init() {
        this.createAssistantUI();
        this.initSpeechRecognition();
        this.bindEvents();
    }

    createAssistantUI() {
        // Create assistant container
        const assistantContainer = document.createElement('div');
        assistantContainer.id = 'voice-assistant';
        assistantContainer.innerHTML = `
            <div class="assistant-button" id="assistantButton">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                    <path d="M19 10V12C19 16.42 15.42 20 11 20H9V22H11C16.52 22 21 17.52 21 12V10H19Z" fill="currentColor"/>
                    <path d="M5 10V12C5 15.31 7.69 18 11 18V20C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                </svg>
            </div>
            <div class="assistant-panel" id="assistantPanel">
                <div class="assistant-header">
                    <h3>Voice Assistant</h3>
                    <button class="close-btn" id="closeAssistant">&times;</button>
                </div>
                <div class="assistant-content">
                    <div class="status-indicator" id="statusIndicator">
                        <span class="status-text">Click microphone to start</span>
                    </div>
                    <div class="voice-controls">
                        <button class="voice-btn" id="startListening">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                <path d="M19 10V12C19 16.42 15.42 20 11 20H9V22H11C16.52 22 21 17.52 21 12V10H19Z" fill="currentColor"/>
                                <path d="M5 10V12C5 15.31 7.69 18 11 18V20C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                            </svg>
                            Start Listening
                        </button>
                    </div>
                    <div class="transcript" id="transcript"></div>
                    <div class="response" id="response"></div>
                </div>
                <div class="assistant-footer">
                    <small>Try: "Open home page", "Go to about", "Navigate to contact"</small>
                </div>
            </div>
        `;

        // Add CSS styles
        const styles = `
            <style>
                #voice-assistant {
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    z-index: 10000;
                    font-family: Arial, sans-serif;
                }

                .assistant-button {
                    width: 60px;
                    height: 60px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    transition: all 0.3s ease;
                    color: white;
                }

                .assistant-button:hover {
                    transform: scale(1.1);
                    box-shadow: 0 6px 25px rgba(0,0,0,0.4);
                }

                .assistant-button.listening {
                    animation: pulse 1.5s infinite;
                    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
                }

                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                    100% { transform: scale(1); }
                }

                .assistant-panel {
                    position: absolute;
                    bottom: 80px;
                    right: 0;
                    width: 350px;
                    background: white;
                    border-radius: 15px;
                    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
                    display: none;
                    overflow: hidden;
                }

                .assistant-panel.active {
                    display: block;
                    animation: slideUp 0.3s ease;
                }

                @keyframes slideUp {
                    from { opacity: 0; transform: translateY(20px); }
                    to { opacity: 1; transform: translateY(0); }
                }

                .assistant-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 15px 20px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .assistant-header h3 {
                    margin: 0;
                    font-size: 16px;
                }

                .close-btn {
                    background: none;
                    border: none;
                    color: white;
                    font-size: 24px;
                    cursor: pointer;
                    padding: 0;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                    transition: background-color 0.3s ease;
                }

                .close-btn:hover {
                    background-color: rgba(255,255,255,0.2);
                }

                .assistant-content {
                    padding: 20px;
                }

                .status-indicator {
                    text-align: center;
                    margin-bottom: 15px;
                    padding: 10px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                }

                .status-text {
                    font-size: 14px;
                    color: #666;
                }

                .status-indicator.listening .status-text {
                    color: #e74c3c;
                    font-weight: bold;
                }

                .voice-controls {
                    text-align: center;
                    margin-bottom: 15px;
                }

                .voice-btn {
                    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
                    color: white;
                    border: none;
                    padding: 12px 20px;
                    border-radius: 25px;
                    cursor: pointer;
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin: 0 auto;
                    transition: all 0.3s ease;
                }

                .voice-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
                }

                .voice-btn:disabled {
                    background: #95a5a6;
                    cursor: not-allowed;
                    transform: none;
                    box-shadow: none;
                }

                .transcript, .response {
                    margin-bottom: 10px;
                    padding: 10px;
                    border-radius: 8px;
                    font-size: 14px;
                    min-height: 20px;
                }

                .transcript {
                    background-color: #e8f4fd;
                    border-left: 4px solid #3498db;
                }

                .response {
                    background-color: #e8f5e8;
                    border-left: 4px solid #27ae60;
                }

                .assistant-footer {
                    background-color: #f8f9fa;
                    padding: 10px 20px;
                    text-align: center;
                }

                .assistant-footer small {
                    color: #666;
                    font-size: 12px;
                }

                @media (max-width: 480px) {
                    .assistant-panel {
                        width: 300px;
                        right: -10px;
                    }
                }
            </style>
        `;

        // Add styles to head
        document.head.insertAdjacentHTML('beforeend', styles);
        
        // Add assistant to body
        document.body.appendChild(assistantContainer);
    }

    initSpeechRecognition() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            
            this.recognition.continuous = false;
            this.recognition.interimResults = true;
            this.recognition.lang = 'en-US';

            this.recognition.onstart = () => {
                this.isListening = true;
                this.updateUI();
            };

            this.recognition.onresult = (event) => {
                let transcript = '';
                for (let i = event.resultIndex; i < event.results.length; i++) {
                    transcript += event.results[i][0].transcript;
                }
                this.displayTranscript(transcript);
                
                if (event.results[event.results.length - 1].isFinal) {
                    this.processCommand(transcript);
                }
            };

            this.recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
                this.displayResponse(`Error: ${event.error}`, 'error');
                this.isListening = false;
                this.updateUI();
            };

            this.recognition.onend = () => {
                this.isListening = false;
                this.updateUI();
            };
        } else {
            this.displayResponse('Speech recognition not supported in this browser', 'error');
        }
    }

    bindEvents() {
        const assistantButton = document.getElementById('assistantButton');
        const assistantPanel = document.getElementById('assistantPanel');
        const closeAssistant = document.getElementById('closeAssistant');
        const startListening = document.getElementById('startListening');

        assistantButton.addEventListener('click', () => {
            assistantPanel.classList.toggle('active');
        });

        closeAssistant.addEventListener('click', () => {
            assistantPanel.classList.remove('active');
        });

        startListening.addEventListener('click', () => {
            if (this.isListening) {
                this.stopListening();
            } else {
                this.startListening();
            }
        });

        // Close panel when clicking outside
        document.addEventListener('click', (e) => {
            if (!assistantButton.contains(e.target) && !assistantPanel.contains(e.target)) {
                assistantPanel.classList.remove('active');
            }
        });
    }

    startListening() {
        if (this.recognition && !this.isListening) {
            this.clearMessages();
            this.recognition.start();
        }
    }

    stopListening() {
        if (this.recognition && this.isListening) {
            this.recognition.stop();
        }
    }

    async processCommand(transcript) {
        try {
            this.displayResponse('Processing...', 'info');
            
            const response = await fetch(`${this.apiUrl}/process-command`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ text: transcript })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.action === 'navigate' && data.page) {
                this.displayResponse(data.message, 'success');
                setTimeout(() => {
                    window.location.href = data.page;
                }, 1000);
            } else {
                this.displayResponse(data.message, 'info');
            }
            
        } catch (error) {
            console.error('Error processing command:', error);
            this.displayResponse('Sorry, I couldn\'t process that command. Please try again.', 'error');
        }
    }

    displayTranscript(text) {
        const transcriptEl = document.getElementById('transcript');
        transcriptEl.textContent = `You said: "${text}"`;
    }

    displayResponse(text, type = 'info') {
        const responseEl = document.getElementById('response');
        responseEl.textContent = text;
        responseEl.className = `response ${type}`;
    }

    clearMessages() {
        document.getElementById('transcript').textContent = '';
        document.getElementById('response').textContent = '';
    }

    updateUI() {
        const assistantButton = document.getElementById('assistantButton');
        const statusIndicator = document.getElementById('statusIndicator');
        const startListening = document.getElementById('startListening');

        if (this.isListening) {
            assistantButton.classList.add('listening');
            statusIndicator.classList.add('listening');
            statusIndicator.querySelector('.status-text').textContent = 'Listening...';
            startListening.textContent = 'Stop Listening';
            startListening.disabled = false;
        } else {
            assistantButton.classList.remove('listening');
            statusIndicator.classList.remove('listening');
            statusIndicator.querySelector('.status-text').textContent = 'Click microphone to start';
            startListening.innerHTML = `
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                    <path d="M19 10V12C19 16.42 15.42 20 11 20H9V22H11C16.52 22 21 17.52 21 12V10H19Z" fill="currentColor"/>
                    <path d="M5 10V12C5 15.31 7.69 18 11 18V20C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                </svg>
                Start Listening
            `;
            startListening.disabled = false;
        }
    }
}

// Initialize the voice assistant when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new VoiceNavigationAssistant();
});
