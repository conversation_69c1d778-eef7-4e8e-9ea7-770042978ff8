import requests
import json

def test_api():
    url = "http://localhost:8000/process-command"

    test_commands = [
        "open home page",
        "go to about",
        "navigate to contact",
        "take me to login",
        "show me register",
        "what's the weather"  # This should return unknown
    ]

    for command in test_commands:
        data = {"text": command}
        try:
            response = requests.post(url, json=data)
            print(f"Command: '{command}'")
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.json()}")
            print("-" * 50)
        except Exception as e:
            print(f"Error with '{command}': {e}")
            print("-" * 50)

if __name__ == "__main__":
    test_api()
