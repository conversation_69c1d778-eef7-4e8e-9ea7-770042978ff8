from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import openai
import os
from dotenv import load_dotenv
import json
import re

# Load environment variables
load_dotenv()

app = FastAPI(title="Voice Navigation Assistant", version="1.0.0")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize OpenAI client
openai.api_key = os.getenv("OPENAI_API_KEY")

class VoiceCommand(BaseModel):
    text: str

class NavigationResponse(BaseModel):
    action: str
    page: str
    message: str

# Navigation mapping
NAVIGATION_PAGES = {
    "home": "index.html",
    "homepage": "index.html",
    "main": "index.html",
    "about": "about.html",
    "about us": "about.html",
    "contact": "contact.html",
    "contact us": "contact.html",
    "login": "login.html",
    "sign in": "login.html",
    "register": "register.html",
    "signup": "register.html",
    "sign up": "register.html"
}

def extract_navigation_intent(text: str) -> dict:
    """Extract navigation intent from user text using OpenAI"""
    try:
        prompt = f"""
        You are a navigation assistant for a website. The user said: "{text}"
        
        Available pages are:
        - Home/Homepage/Main (index.html)
        - About/About Us (about.html)
        - Contact/Contact Us (contact.html)
        - Login/Sign In (login.html)
        - Register/Sign Up/Signup (register.html)
        
        Analyze the user's request and respond with a JSON object containing:
        {{
            "action": "navigate" or "unknown",
            "page": "page_name" or null,
            "confidence": 0.0 to 1.0
        }}
        
        Examples:
        - "open home page" -> {{"action": "navigate", "page": "home", "confidence": 0.9}}
        - "go to about" -> {{"action": "navigate", "page": "about", "confidence": 0.8}}
        - "take me to login" -> {{"action": "navigate", "page": "login", "confidence": 0.9}}
        - "what's the weather" -> {{"action": "unknown", "page": null, "confidence": 0.1}}
        
        Only respond with the JSON object, no other text.
        """
        
        response = openai.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=100,
            temperature=0.3
        )
        
        result = response.choices[0].message.content.strip()
        return json.loads(result)
        
    except Exception as e:
        print(f"OpenAI API error: {e}")
        # Fallback to simple keyword matching
        return fallback_navigation_intent(text)

def fallback_navigation_intent(text: str) -> dict:
    """Fallback navigation intent extraction using keyword matching"""
    text_lower = text.lower()
    
    # Check for navigation keywords
    nav_keywords = ["open", "go to", "navigate", "take me", "show me", "visit"]
    has_nav_keyword = any(keyword in text_lower for keyword in nav_keywords)
    
    if not has_nav_keyword:
        return {"action": "unknown", "page": None, "confidence": 0.1}
    
    # Check for page keywords
    for page_key, page_file in NAVIGATION_PAGES.items():
        if page_key in text_lower:
            return {"action": "navigate", "page": page_key, "confidence": 0.7}
    
    return {"action": "unknown", "page": None, "confidence": 0.2}

@app.get("/")
async def root():
    return {"message": "Voice Navigation Assistant API is running"}

@app.post("/process-command", response_model=NavigationResponse)
async def process_voice_command(command: VoiceCommand):
    """Process voice command and return navigation response"""
    try:
        if not command.text.strip():
            raise HTTPException(status_code=400, detail="Empty command text")
        
        # Extract navigation intent
        intent = extract_navigation_intent(command.text)
        
        if intent["action"] == "navigate" and intent["page"]:
            page_key = intent["page"]
            page_file = NAVIGATION_PAGES.get(page_key, "index.html")
            
            return NavigationResponse(
                action="navigate",
                page=page_file,
                message=f"Navigating to {page_key} page"
            )
        else:
            return NavigationResponse(
                action="unknown",
                page="",
                message="I didn't understand that navigation command. Try saying 'open home page' or 'go to about'"
            )
            
    except Exception as e:
        print(f"Error processing command: {e}")
        raise HTTPException(status_code=500, detail="Error processing voice command")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "Voice Navigation Assistant is running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
